#!/usr/bin/env python3
"""
测试FCE参数解析的脚本
验证命令行参数是否正确传递到训练脚本中
"""

import sys
import os

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_fce_args():
    """测试FCE参数解析"""
    print("测试FCE参数解析...")
    
    # 模拟命令行参数
    test_args = [
        'test_script.py',
        '--dataset', 'CIRR',
        '--enable_fce',
        '--fce_weight', '0.5',
        '--combine_fce_with_original',
        '--positive_loss', 'RCL',
        '--trade_off', '1.0',
        '--batch_size', '64',
        '--num_epochs', '5',
        '--exp_name', 'test_fce'
    ]
    
    # 备份原始sys.argv
    original_argv = sys.argv.copy()
    
    try:
        # 设置测试参数
        sys.argv = test_args
        
        # 导入并测试参数解析
        from precompute_train import parse_args
        
        args = parse_args()
        
        print("参数解析成功！")
        print(f"  dataset: {args.dataset}")
        print(f"  enable_fce: {args.enable_fce}")
        print(f"  fce_weight: {args.fce_weight}")
        print(f"  combine_fce_with_original: {args.combine_fce_with_original}")
        print(f"  positive_loss: {args.positive_loss}")
        print(f"  trade_off: {args.trade_off}")
        print(f"  batch_size: {args.batch_size}")
        print(f"  exp_name: {args.exp_name}")
        
        # 验证FCE参数
        assert args.enable_fce == True, "enable_fce应该为True"
        assert args.fce_weight == 0.5, "fce_weight应该为0.5"
        assert args.combine_fce_with_original == True, "combine_fce_with_original应该为True"
        
        print("✓ 所有FCE参数解析正确！")
        return True
        
    except Exception as e:
        print(f"✗ 参数解析失败: {e}")
        return False
    finally:
        # 恢复原始sys.argv
        sys.argv = original_argv

def test_pn_loss_config():
    """测试pn_loss配置构建"""
    print("\n测试pn_loss配置构建...")
    
    # 模拟args对象
    class MockArgs:
        def __init__(self):
            self.positive_loss = 'RCL'
            self.negative_loss = 'None'
            self.trade_off = 1.0
            self.enable_fce = True
            self.fce_weight = 0.5
            self.combine_fce_with_original = True
    
    args = MockArgs()
    
    # 构建pn_loss配置
    pn_loss = {
        'positive_loss': args.positive_loss,
        'negative_loss': args.negative_loss, 
        'trade_off': args.trade_off,
        'positive_align_loss': 'None',
        'negative_align_loss': 'None',
        'trade_off_align': 1.0,
        'warmup_loss': args.positive_loss,
        'warmup_align_loss': 'None',
        
        # FCE设置
        'enable_fce': args.enable_fce,
        'fce_weight': args.fce_weight,
        'combine_fce_with_original': args.combine_fce_with_original
    }
    
    print("pn_loss配置:")
    for key, value in pn_loss.items():
        if 'fce' in key.lower():
            print(f"  {key}: {value}")
    
    # 验证配置
    assert pn_loss['enable_fce'] == True, "enable_fce配置错误"
    assert pn_loss['fce_weight'] == 0.5, "fce_weight配置错误"
    assert pn_loss['combine_fce_with_original'] == True, "combine_fce_with_original配置错误"
    
    print("✓ pn_loss配置构建正确！")
    return True

def test_shell_script_args():
    """测试shell脚本参数"""
    print("\n测试shell脚本参数格式...")
    
    # 模拟train.sh中的参数
    shell_args = [
        '--enable_fce',
        '--fce_weight', '0.5',
        '--combine_fce_with_original'
    ]
    
    print("Shell脚本参数:")
    for arg in shell_args:
        print(f"  {arg}")
    
    print("✓ Shell脚本参数格式正确！")
    return True

def main():
    """运行所有测试"""
    print("FCE参数解析测试")
    print("=" * 50)
    
    tests = [
        test_fce_args,
        test_pn_loss_config,
        test_shell_script_args
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试 {test.__name__} 失败: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有FCE参数测试通过！")
        print("\n现在可以使用以下命令启动FCE训练:")
        print("  bash train.sh")
        print("  或者:")
        print("  python src/precompute_train.py --enable_fce --fce_weight 0.5 --combine_fce_with_original")
    else:
        print("✗ 部分测试失败，请检查配置")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
