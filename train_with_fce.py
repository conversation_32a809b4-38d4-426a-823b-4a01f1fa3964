#!/usr/bin/env python3
"""
使用FCE (Fusion with Component-level Examples) 硬负样本训练脚本

这个脚本演示如何启用和配置FCE损失来训练组合图像检索模型。
FCE通过创建合成组合样本作为硬负样本来改进对比学习。

使用方法:
    python train_with_fce.py --enable_fce --fce_weight 0.5
"""

import argparse
import sys
import os

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='使用FCE硬负样本训练组合图像检索模型')
    
    # 基本训练参数
    parser.add_argument('--dataset', type=str, default='CIRR', 
                       choices=['CIRR', 'FashionIQ'],
                       help='数据集选择')
    parser.add_argument('--num_epochs', type=int, default=5,
                       help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=64,
                       help='批次大小')
    parser.add_argument('--learning_rate', type=float, default=1e-5,
                       help='学习率')
    
    # FCE相关参数
    parser.add_argument('--enable_fce', action='store_true',
                       help='启用FCE硬负样本损失')
    parser.add_argument('--fce_weight', type=float, default=0.5,
                       help='FCE损失权重 (0.0-1.0)')
    parser.add_argument('--combine_fce_with_original', action='store_true', default=True,
                       help='是否将FCE损失与原始损失结合')
    
    # 损失函数参数
    parser.add_argument('--positive_loss', type=str, default='RCL',
                       choices=['RCL', 'infoNCE'],
                       help='正样本损失函数')
    parser.add_argument('--negative_loss', type=str, default='None',
                       help='负样本损失函数')
    parser.add_argument('--trade_off', type=float, default=1.0,
                       help='正负样本损失权衡参数')
    
    # 其他参数
    parser.add_argument('--exp_name', type=str, default='fce_experiment',
                       help='实验名称')
    parser.add_argument('--seed', type=int, default=42,
                       help='随机种子')
    parser.add_argument('--shuffle_seed', type=int, default=42,
                       help='数据打乱种子')
    parser.add_argument('--noise_ratio', type=float, default=0.0,
                       help='噪声比例')
    parser.add_argument('--timestamp', type=str, default='',
                       help='时间戳')
    
    return parser.parse_args()

def print_fce_config(args):
    """打印FCE配置信息"""
    print("=" * 60)
    print("FCE (Fusion with Component-level Examples) 训练配置")
    print("=" * 60)
    print(f"数据集: {args.dataset}")
    print(f"训练轮数: {args.num_epochs}")
    print(f"批次大小: {args.batch_size}")
    print(f"学习率: {args.learning_rate}")
    print()
    print("FCE配置:")
    print(f"  启用FCE: {args.enable_fce}")
    if args.enable_fce:
        print(f"  FCE权重: {args.fce_weight}")
        print(f"  与原始损失结合: {args.combine_fce_with_original}")
        print()
        print("FCE工作原理:")
        print("  - 传统方法使用完全不同的查询作为负样本")
        print("  - FCE使用合成组合样本: f'_i = r + s'_i")
        print("  - 将当前参考图像与其他文本特征组合")
        print("  - 创建更难的负样本，提供更好的学习信号")
    else:
        print("  FCE未启用，使用传统对比学习")
    print()
    print("损失函数配置:")
    print(f"  正样本损失: {args.positive_loss}")
    print(f"  负样本损失: {args.negative_loss}")
    print(f"  权衡参数: {args.trade_off}")
    print("=" * 60)

def main():
    """主函数"""
    args = parse_args()
    
    # 打印配置信息
    print_fce_config(args)
    
    # 验证FCE参数
    if args.enable_fce:
        if not (0.0 <= args.fce_weight <= 1.0):
            print("错误: FCE权重必须在0.0到1.0之间")
            sys.exit(1)
        
        if args.batch_size < 2:
            print("警告: FCE需要批次大小至少为2才能工作")
            print("建议使用批次大小 >= 4 以获得更好的FCE效果")
    
    # 导入训练模块
    try:
        from precompute_train import main as train_main
        print("开始训练...")
        
        # 调用训练函数
        train_main(args)
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保在正确的目录中运行此脚本")
        sys.exit(1)
    except Exception as e:
        print(f"训练过程中出现错误: {e}")
        sys.exit(1)

def show_usage_examples():
    """显示使用示例"""
    examples = """
使用示例:

1. 启用FCE训练 (推荐):
   python train_with_fce.py --enable_fce --fce_weight 0.5 --batch_size 64

2. 不同FCE权重的实验:
   python train_with_fce.py --enable_fce --fce_weight 0.3 --exp_name fce_weight_03
   python train_with_fce.py --enable_fce --fce_weight 0.7 --exp_name fce_weight_07

3. 传统训练 (不使用FCE):
   python train_with_fce.py --exp_name traditional_training

4. FashionIQ数据集训练:
   python train_with_fce.py --dataset FashionIQ --enable_fce --fce_weight 0.5

5. 长时间训练:
   python train_with_fce.py --enable_fce --num_epochs 20 --learning_rate 5e-6

注意事项:
- FCE需要批次大小至少为2
- 建议FCE权重在0.3-0.7之间
- 较大的批次大小通常能获得更好的FCE效果
- 可以通过实验不同的FCE权重来找到最佳配置
"""
    print(examples)

if __name__ == "__main__":
    if len(sys.argv) == 1:
        print("FCE训练脚本")
        print("使用 --help 查看所有参数")
        print("使用 --examples 查看使用示例")
        sys.exit(0)
    
    if '--examples' in sys.argv:
        show_usage_examples()
        sys.exit(0)
    
    main()
