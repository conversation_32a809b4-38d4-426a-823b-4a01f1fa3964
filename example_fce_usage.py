"""
Example usage of FCE (Fusion with Component-level Examples) hard negative loss
for the BLIP2 Composed Image Retrieval model.

This demonstrates how to configure and use the FCE loss implementation
based on the paper "Learning with Synthetic Composition Examples".
"""

import torch
from lavis.models import load_model

def example_fce_configuration():
    """
    Example of how to configure FCE loss in the pn_loss dictionary.
    """
    
    # Basic configuration with FCE enabled
    pn_loss_with_fce = {
        # Original loss configuration
        'positive_loss': 'RCL',  # or 'infoNCE'
        'negative_loss': 'None', 
        'trade_off': 1.0,
        'positive_align_loss': 'None',
        'negative_align_loss': 'None',
        'trade_off_align': 1.0,
        'warmup_loss': 'RCL',
        'warmup_align_loss': 'None',
        
        # FCE-specific configuration
        'enable_fce': True,  # Enable FCE hard negative mining
        'fce_weight': 0.5,   # Weight for FCE loss (0.0 to 1.0)
        'combine_fce_with_original': True,  # Add FCE loss to original loss
    }
    
    return pn_loss_with_fce

def example_training_with_fce():
    """
    Example of how to use FCE loss during training.
    """
    
    # Load the model
    model = load_model("blip2_cir_image_diff_features", "pretrain")
    model.train()
    
    # Configure FCE loss
    pn_loss = example_fce_configuration()
    
    # Example batch data (replace with your actual data)
    batch_size = 4
    samples = {
        "image": torch.randn(batch_size, 3, 224, 224),  # Reference images
        "target": torch.randn(batch_size, 3, 224, 224),  # Target images  
        "text_input": [
            "a red car",
            "a blue house", 
            "a green tree",
            "a yellow flower"
        ]
    }
    
    # Labels (1 for clean, 0 for noisy samples)
    labels = torch.ones(batch_size)  # All clean for this example
    
    # Forward pass with FCE loss
    with torch.cuda.amp.autocast():
        loss_dict = model(samples, labels, pn_loss, warmup=False)
    
    print("Loss components:")
    for key, value in loss_dict.items():
        print(f"  {key}: {value.item():.4f}")
    
    # The loss_dict will contain:
    # - 'lrm': Original robust InfoNCE loss
    # - 'fce_loss': FCE hard negative loss (if enabled)
    # - 'total_loss': Combined loss (if combine_fce_with_original=True)
    
    return loss_dict

def fce_loss_explanation():
    """
    Explanation of how FCE loss works.
    """
    explanation = """
    FCE (Fusion with Component-level Examples) Loss Explanation:
    
    1. Traditional Approach:
       - Uses query-level negative examples: q'_i (completely different queries)
       - Easy negatives that provide little learning signal
    
    2. FCE Approach:
       - Uses component-level negative examples: f'_i = r + s'_i
       - Combines current reference image (r) with other text features (s'_i)
       - Creates harder negatives that are closer to the target
    
    3. Loss Formula (Equation 4 from paper):
       L2 = -log(exp(κ(t,q)) / (exp(κ(t,q)) + Σ exp(κ(t,f'_i))))
       
       Where:
       - t: target image embedding
       - q: positive composed query (r + s)
       - f'_i: synthetic composition examples (r + s'_i)
       - κ: cosine similarity function
    
    4. Benefits:
       - Harder negatives improve learning
       - Better fusion between image and text modalities
       - More effective contrastive learning
    
    5. Configuration Options:
       - enable_fce: Turn FCE loss on/off
       - fce_weight: Control contribution of FCE loss
       - combine_fce_with_original: Whether to add to original loss
    """
    print(explanation)

if __name__ == "__main__":
    print("FCE Loss Configuration Example")
    print("=" * 50)
    
    # Show configuration
    config = example_fce_configuration()
    print("Example FCE configuration:")
    for key, value in config.items():
        if 'fce' in key.lower():
            print(f"  {key}: {value}")
    
    print("\n" + "=" * 50)
    fce_loss_explanation()
    
    print("\n" + "=" * 50)
    print("For actual training, uncomment the following line:")
    print("# loss_dict = example_training_with_fce()")
