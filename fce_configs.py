"""
FCE (Fusion with Component-level Examples) 配置文件

这个文件包含了不同的FCE配置示例，用于不同的实验场景。
"""

# 基础FCE配置
BASIC_FCE_CONFIG = {
    'positive_loss': 'RCL',
    'negative_loss': 'None', 
    'trade_off': 1.0,
    'positive_align_loss': 'None',
    'negative_align_loss': 'None',
    'trade_off_align': 1.0,
    'warmup_loss': 'RCL',
    'warmup_align_loss': 'None',
    
    # FCE设置
    'enable_fce': True,
    'fce_weight': 0.5,
    'combine_fce_with_original': True
}

# 强FCE配置 - 更高的FCE权重
STRONG_FCE_CONFIG = {
    **BASIC_FCE_CONFIG,
    'fce_weight': 0.7,
}

# 弱FCE配置 - 较低的FCE权重
WEAK_FCE_CONFIG = {
    **BASIC_FCE_CONFIG,
    'fce_weight': 0.3,
}

# 纯FCE配置 - 只使用FCE损失
PURE_FCE_CONFIG = {
    **BASIC_FCE_CONFIG,
    'fce_weight': 1.0,
    'combine_fce_with_original': False
}

# 传统配置 - 不使用FCE
TRADITIONAL_CONFIG = {
    'positive_loss': 'RCL',
    'negative_loss': 'None', 
    'trade_off': 1.0,
    'positive_align_loss': 'None',
    'negative_align_loss': 'None',
    'trade_off_align': 1.0,
    'warmup_loss': 'RCL',
    'warmup_align_loss': 'None',
    
    # FCE禁用
    'enable_fce': False,
    'fce_weight': 0.0,
    'combine_fce_with_original': False
}

# InfoNCE + FCE配置
INFONCE_FCE_CONFIG = {
    'positive_loss': 'infoNCE',
    'negative_loss': 'None', 
    'trade_off': 1.0,
    'positive_align_loss': 'None',
    'negative_align_loss': 'None',
    'trade_off_align': 1.0,
    'warmup_loss': 'infoNCE',
    'warmup_align_loss': 'None',
    
    # FCE设置
    'enable_fce': True,
    'fce_weight': 0.5,
    'combine_fce_with_original': True
}

# 实验配置字典
EXPERIMENT_CONFIGS = {
    'basic_fce': BASIC_FCE_CONFIG,
    'strong_fce': STRONG_FCE_CONFIG,
    'weak_fce': WEAK_FCE_CONFIG,
    'pure_fce': PURE_FCE_CONFIG,
    'traditional': TRADITIONAL_CONFIG,
    'infonce_fce': INFONCE_FCE_CONFIG,
}

def get_config(config_name):
    """
    获取指定名称的配置
    
    Args:
        config_name (str): 配置名称
        
    Returns:
        dict: 配置字典
    """
    if config_name not in EXPERIMENT_CONFIGS:
        available = ', '.join(EXPERIMENT_CONFIGS.keys())
        raise ValueError(f"未知配置: {config_name}. 可用配置: {available}")
    
    return EXPERIMENT_CONFIGS[config_name].copy()

def print_config(config_name):
    """
    打印指定配置的详细信息
    
    Args:
        config_name (str): 配置名称
    """
    config = get_config(config_name)
    
    print(f"配置: {config_name}")
    print("=" * 40)
    
    # 基础损失设置
    print("基础损失设置:")
    print(f"  正样本损失: {config['positive_loss']}")
    print(f"  负样本损失: {config['negative_loss']}")
    print(f"  权衡参数: {config['trade_off']}")
    
    # FCE设置
    print("\nFCE设置:")
    print(f"  启用FCE: {config['enable_fce']}")
    if config['enable_fce']:
        print(f"  FCE权重: {config['fce_weight']}")
        print(f"  与原始损失结合: {config['combine_fce_with_original']}")
    
    print()

def print_all_configs():
    """打印所有可用配置"""
    print("所有可用的FCE配置:")
    print("=" * 50)
    
    for name in EXPERIMENT_CONFIGS.keys():
        print_config(name)

def get_recommended_config(dataset='CIRR', batch_size=64):
    """
    根据数据集和批次大小推荐配置
    
    Args:
        dataset (str): 数据集名称
        batch_size (int): 批次大小
        
    Returns:
        dict: 推荐的配置
    """
    if batch_size < 2:
        print("警告: 批次大小小于2，FCE无法工作，使用传统配置")
        return get_config('traditional')
    
    if batch_size < 8:
        print("批次大小较小，推荐使用弱FCE配置")
        return get_config('weak_fce')
    elif batch_size >= 32:
        print("批次大小较大，推荐使用强FCE配置")
        return get_config('strong_fce')
    else:
        print("批次大小适中，推荐使用基础FCE配置")
        return get_config('basic_fce')

# 配置描述
CONFIG_DESCRIPTIONS = {
    'basic_fce': '基础FCE配置，适合大多数情况，FCE权重0.5',
    'strong_fce': '强FCE配置，更高的FCE权重0.7，适合大批次训练',
    'weak_fce': '弱FCE配置，较低的FCE权重0.3，适合小批次或初始实验',
    'pure_fce': '纯FCE配置，只使用FCE损失，用于研究FCE的纯效果',
    'traditional': '传统配置，不使用FCE，用作基线对比',
    'infonce_fce': 'InfoNCE+FCE配置，使用InfoNCE作为基础损失函数',
}

def show_config_guide():
    """显示配置选择指南"""
    guide = """
FCE配置选择指南:

1. 首次使用FCE:
   推荐: basic_fce
   原因: 平衡的FCE权重，适合大多数场景

2. 大批次训练 (batch_size >= 32):
   推荐: strong_fce
   原因: 大批次提供更多负样本，可以使用更高的FCE权重

3. 小批次训练 (batch_size < 8):
   推荐: weak_fce
   原因: 小批次负样本较少，使用较低的FCE权重

4. 研究FCE纯效果:
   推荐: pure_fce
   原因: 只使用FCE损失，便于分析FCE的贡献

5. 基线对比:
   推荐: traditional
   原因: 不使用FCE，作为对比基线

6. 不同损失函数实验:
   推荐: infonce_fce
   原因: 使用InfoNCE而不是RCL作为基础损失

使用建议:
- 从basic_fce开始实验
- 根据结果调整fce_weight
- 比较不同配置的效果
- 记录最佳配置用于最终训练
"""
    print(guide)

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == '--all':
            print_all_configs()
        elif sys.argv[1] == '--guide':
            show_config_guide()
        elif sys.argv[1] in EXPERIMENT_CONFIGS:
            print_config(sys.argv[1])
        else:
            print(f"未知参数: {sys.argv[1]}")
            print("可用参数: --all, --guide, 或配置名称")
            print(f"可用配置: {', '.join(EXPERIMENT_CONFIGS.keys())}")
    else:
        print("FCE配置文件")
        print("使用方法:")
        print("  python fce_configs.py --all      # 显示所有配置")
        print("  python fce_configs.py --guide    # 显示配置选择指南")
        print("  python fce_configs.py basic_fce  # 显示特定配置")
        print(f"\n可用配置: {', '.join(EXPERIMENT_CONFIGS.keys())}")
