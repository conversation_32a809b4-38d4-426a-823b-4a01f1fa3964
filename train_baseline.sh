#!/bin/bash
# 基线训练脚本 - 不使用FCE，仅使用传统lrm损失函数
# 用于与FCE训练结果进行对比

your_exp_name=8-18-baseline-no-fce
shuffle_seed=42
seed=42 
dataset=CIRR
# 指定使用的GPU编号，注意这里直接使用数字，不要加引号
gpu=1

noise_ratio=0.0

echo "指定使用GPU: ${gpu}"
echo "基线训练: 不使用FCE硬负样本"

python src/precompute_train.py \
    --exp_name "${your_exp_name}" \
    --shuffle_seed ${shuffle_seed} \
    --seed ${seed} \
    --dataset ${dataset} \
    --noise_ratio ${noise_ratio} \
    --batch_size 64 \
    --num_epochs 30 \
    --lr "1e-5" \
    --positive_loss "RCL" \
    --trade_off 1.0 \
    --save_training \
    --gpu ${gpu}
