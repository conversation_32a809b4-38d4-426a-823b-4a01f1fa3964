#!/bin/bash
# FCE实验脚本 - 测试不同的FCE权重配置
# 用于找到最佳的FCE权重设置

shuffle_seed=42
seed=42 
dataset=CIRR
gpu=1
noise_ratio=0.0

echo "开始FCE权重实验..."
echo "指定使用GPU: ${gpu}"

# 实验1: 弱FCE (权重0.3)
echo "实验1: 弱FCE (权重0.3)"
python src/precompute_train.py \
    --exp_name "fce-weak-0.3" \
    --shuffle_seed ${shuffle_seed} \
    --seed ${seed} \
    --dataset ${dataset} \
    --noise_ratio ${noise_ratio} \
    --batch_size 64 \
    --num_epochs 10 \
    --lr "1e-5" \
    --positive_loss "RCL" \
    --trade_off 1.0 \
    --enable_fce \
    --fce_weight 0.3 \
    --combine_fce_with_original \
    --save_training \
    --gpu ${gpu}

echo "实验1完成，等待5秒..."
sleep 5

# 实验2: 中等FCE (权重0.5)
echo "实验2: 中等FCE (权重0.5)"
python src/precompute_train.py \
    --exp_name "fce-medium-0.5" \
    --shuffle_seed ${shuffle_seed} \
    --seed ${seed} \
    --dataset ${dataset} \
    --noise_ratio ${noise_ratio} \
    --batch_size 64 \
    --num_epochs 10 \
    --lr "1e-5" \
    --positive_loss "RCL" \
    --trade_off 1.0 \
    --enable_fce \
    --fce_weight 0.5 \
    --combine_fce_with_original \
    --save_training \
    --gpu ${gpu}

echo "实验2完成，等待5秒..."
sleep 5

# 实验3: 强FCE (权重0.7)
echo "实验3: 强FCE (权重0.7)"
python src/precompute_train.py \
    --exp_name "fce-strong-0.7" \
    --shuffle_seed ${shuffle_seed} \
    --seed ${seed} \
    --dataset ${dataset} \
    --noise_ratio ${noise_ratio} \
    --batch_size 64 \
    --num_epochs 10 \
    --lr "1e-5" \
    --positive_loss "RCL" \
    --trade_off 1.0 \
    --enable_fce \
    --fce_weight 0.7 \
    --combine_fce_with_original \
    --save_training \
    --gpu ${gpu}

echo "实验3完成，等待5秒..."
sleep 5

# 实验4: 基线对比 (不使用FCE)
echo "实验4: 基线对比 (不使用FCE)"
python src/precompute_train.py \
    --exp_name "baseline-no-fce" \
    --shuffle_seed ${shuffle_seed} \
    --seed ${seed} \
    --dataset ${dataset} \
    --noise_ratio ${noise_ratio} \
    --batch_size 64 \
    --num_epochs 10 \
    --lr "1e-5" \
    --positive_loss "RCL" \
    --trade_off 1.0 \
    --save_training \
    --gpu ${gpu}

echo "所有FCE实验完成！"
echo "请查看各实验的结果并比较性能。"
echo "实验结果保存在对应的实验目录中。"
