"""
 Copyright (c) 2023, salesforce.com, inc.
 All rights reserved.
 SPDX-License-Identifier: BSD-3-Clause
 For full license text, see the LICENSE file in the repo root or https://opensource.org/licenses/BSD-3-Clause
"""
import logging

import torch
import torch.nn as nn
from torch.cuda.amp import autocast as autocast
from torch.nn import functional as F

from lavis.common.registry import registry
from lavis.models.blip2_models.blip2 import (
    Blip2Base,
    disabled_train,
)
from utility import get_closs

@registry.register_model("blip2_cir_image_diff_features")
class Blip2QformerCirImageDiffFeatures(Blip2Base):
    """
    BLIP2 model with Q-former and ViT for Composed Image Retrieval with FCE loss support.

    This model implements the FCE (Fusion with Component-level Examples) hard negative
    mining approach from the paper "Learning with Synthetic Composition Examples".

    FCE Configuration in pn_loss:
        enable_fce (bool): Enable FCE loss computation (default: False)
        fce_weight (float): Weight for FCE loss (default: 0.5)
        combine_fce_with_original (bool): Whether to add FCE loss to original loss (default: True)

    Usage:
        >>> from lavis.models import load_model
        >>> model = load_model("blip2_cir_image_diff_features", "pretrain")

        # Example pn_loss configuration with FCE:
        >>> pn_loss = {
        ...     'positive_loss': 'RCL',
        ...     'negative_loss': 'None',
        ...     'trade_off': 1.0,
        ...     'enable_fce': True,
        ...     'fce_weight': 0.5,
        ...     'combine_fce_with_original': True
        ... }
    """

    PRETRAINED_MODEL_CONFIG_DICT = {
        "pretrain": "configs/models/blip2/blip2_pretrain.yaml",
        "pretrain_vitL": "configs/models/blip2/blip2_pretrain_vitL.yaml",
        "coco": "configs/models/blip2/blip2_coco.yaml",
    }

    def __init__(
        self,
        vit_model="eva_clip_g",
        img_size=224,
        drop_path_rate=0,
        use_grad_checkpoint=False,
        vit_precision="fp16",
        freeze_vit=True,
        num_query_token=32,
        cross_attention_freq=2,
        embed_dim=256,
        max_txt_len=32,
    ):
        super().__init__()

        self.tokenizer = self.init_tokenizer()

        self.visual_encoder, self.ln_vision = self.init_vision_encoder(
            vit_model, img_size, drop_path_rate, use_grad_checkpoint, vit_precision
        )
        if freeze_vit:
            for name, param in self.visual_encoder.named_parameters():
                param.requires_grad = False
            self.visual_encoder = self.visual_encoder.eval()
            self.visual_encoder.train = disabled_train
            logging.info("freeze vision encoder")
        self.Qformer, self.query_tokens = self.init_Qformer(
            num_query_token, self.visual_encoder.num_features, cross_attention_freq
        )
        self.Qformer.resize_token_embeddings(len(self.tokenizer))
        state_dict = self.Qformer.state_dict()
        for name, param in self.Qformer.named_parameters():
            if "_query" in name:
                key_orig = name.replace("_query", "")
                param.data.copy_(state_dict[key_orig])

        self.vision_proj = nn.Linear(self.Qformer.config.hidden_size, embed_dim)
        self.text_proj = nn.Linear(self.Qformer.config.hidden_size, embed_dim)

        self.itm_head = nn.Linear(self.Qformer.config.hidden_size, 2)

        self.temp = nn.Parameter(0.07 * torch.ones([]))

        self.max_txt_len = max_txt_len
    
    @torch.no_grad()
    def vit_encode(self, image):
        return self.visual_encoder(image)
    
    # Image Encoder
    def encode_image(self, image_embeds, query_tokens=None, ln=True):
        """ Encode images.
        Args:
            image_embeds (Tensor): Image representations encoded by ViT.
            query_tokens (Tensor): The query tokens of Q-Former.
            ln (Tensor): whether to perform layer norm.
        Returns:
            Tensor: Image representation encoded by Qformer.
        """
        if ln:
            with self.maybe_autocast():
                image_embeds = self.ln_vision(image_embeds)
        if query_tokens is None:
            query_tokens = self.query_tokens.expand(image_embeds.shape[0], -1, -1)
        image_atts = torch.ones(image_embeds.size()[:-1], dtype=torch.long).to(
            image_embeds.device
        )
        image_output = self.Qformer.bert(
            query_embeds=query_tokens,
            encoder_hidden_states=image_embeds,
            encoder_attention_mask=image_atts,
            return_dict=True,
        )
        return image_output.last_hidden_state
    
    # Fusion Encoder
    def encode_fusion(self, F_image, text_tokens, no_image=False):
        """Fuse image representations with texts.
        
        Args:
            F_image (Tensor): Image representation
            text_tokens (Tensor): text_tokens
            no_image (bool, optional): no_image is True if F_image is prompt tokens, Defaults to False.
        """
        bs = text_tokens.input_ids.shape[0]
        image_atts = torch.ones(F_image.shape[:-1], dtype=torch.long).to(
            F_image.device
        )
        attention_mask = torch.cat([image_atts, text_tokens.attention_mask], dim=1)
        assert F_image.shape[:-1] == (bs, 32)
        fusion_output = self.Qformer.bert(
            text_tokens.input_ids,
            query_embeds=F_image,
            attention_mask=attention_mask,
            return_dict=True,
            no_img=no_image,
        )
        token_num = 0 if no_image else 32
        res = F.normalize(self.text_proj(fusion_output.last_hidden_state[:, token_num, :]), dim=-1)
        return res
    
    
    @torch.no_grad()
    def per_loss(self, reference_embeds, target_embeds, captions):
        F_r = self.encode_image(reference_embeds)
        F_t = self.encode_image(target_embeds)
        sim_i2t = self.inference(F_r, F_t, captions)
        loss = - (sim_i2t / self.temp).log_softmax(1).diag()
        return loss, sim_i2t.diag()

    def robust_infoNCE(self, scores, labels, pn_loss):
        eps=1e-7
        self.temp.data = torch.clamp(self.temp.data, min=1e-2)
        i2t = (scores/ self.temp).softmax(1)
        i2t = torch.clamp(i2t, min=eps, max=1-eps)
        target=torch.arange(scores.shape[0]).to(scores.device)
        clean_mask = labels.to(bool)
        noise_mask = ~clean_mask
        ploss = get_closs(i2t[clean_mask], target[clean_mask], pn_loss['positive_loss'])
        nloss = get_closs(i2t[noise_mask], target[noise_mask], pn_loss['negative_loss'])
        trade_off = pn_loss['trade_off']
        return trade_off * ploss + (1 - trade_off) * nloss

    def synthetic_composition_loss(self, z_rm, z_target, F_reference, text_tokens, labels, pn_loss):
        """
        Implement FCE (Fusion with Component-level Examples) loss from the paper.
        Creates synthetic composition examples by combining reference image features
        with different text features from the batch.

        Following Equation (4) from the paper:
        L2 = -log(exp(κ(t,q)) / (exp(κ(t,q)) + Σ exp(κ(t,f'_i))))
        where f'_i = r + s'_i (synthetic composition examples)

        Args:
            z_rm: Composed query embeddings (reference + text) [B, embed_dim]
            z_target: Target image embeddings [B, 32, embed_dim]
            F_reference: Reference image features [B, 32, hidden_size]
            text_tokens: Text tokens for all samples in batch
            labels: Clean/noise labels (unused in this implementation)
            pn_loss: Loss configuration
        """
        batch_size = z_rm.shape[0]
        if batch_size < 2:
            return torch.tensor(0.0, device=z_rm.device, requires_grad=True)

        # Efficiently create all synthetic compositions at once
        # For each reference image, pair it with all other text features
        all_synthetic_comps = []
        all_targets = []
        all_positives = []

        for i in range(batch_size):
            current_ref = F_reference[i:i+1]  # [1, 32, hidden_size]
            current_target = z_target[i:i+1]  # [1, 32, embed_dim]
            positive_query = z_rm[i:i+1]  # [1, embed_dim]

            # Create synthetic compositions with all other text features
            synthetic_batch = []
            for j in range(batch_size):
                if i != j:  # Skip self-pairing to avoid trivial negatives
                    # Create text tokens for sample j
                    other_text_tokens = type(text_tokens)(
                        input_ids=text_tokens.input_ids[j:j+1],
                        attention_mask=text_tokens.attention_mask[j:j+1]
                    )
                    # Create synthetic composition: current_ref + other_text
                    synthetic_comp = self.encode_fusion(current_ref, other_text_tokens)
                    synthetic_batch.append(synthetic_comp)

            if synthetic_batch:
                synthetic_comps = torch.cat(synthetic_batch, dim=0)  # [B-1, embed_dim]
                all_synthetic_comps.append(synthetic_comps)
                all_targets.append(current_target.expand(synthetic_comps.shape[0], -1, -1))
                all_positives.append(positive_query.expand(synthetic_comps.shape[0], -1))

        if not all_synthetic_comps:
            return torch.tensor(0.0, device=z_rm.device, requires_grad=True)

        # Calculate FCE loss efficiently
        total_loss = 0.0
        eps = 1e-7

        for i in range(len(all_synthetic_comps)):
            synthetic_comps = all_synthetic_comps[i]  # [B-1, embed_dim]
            target_embeds = all_targets[i]  # [B-1, 32, embed_dim]
            positive_query = all_positives[i]  # [B-1, embed_dim]

            # Calculate positive similarity (same for all negatives in this batch)
            pos_sim = torch.matmul(
                positive_query[:1].unsqueeze(1).unsqueeze(1),
                target_embeds[:1].permute(0, 2, 1)
            ).squeeze()
            pos_sim, _ = pos_sim.max(-1) if pos_sim.dim() > 0 else (pos_sim, None)

            # Calculate negative similarities
            neg_sims = torch.matmul(
                synthetic_comps.unsqueeze(1).unsqueeze(1),
                target_embeds[:1].permute(0, 2, 1)
            ).squeeze()
            if neg_sims.dim() > 1:
                neg_sims, _ = neg_sims.max(-1)

            # FCE InfoNCE loss (Equation 4 from paper)
            pos_exp = torch.exp(pos_sim / self.temp)
            neg_exp = torch.exp(neg_sims / self.temp).sum()

            sample_loss = -torch.log(pos_exp / (pos_exp + neg_exp + eps))
            total_loss += sample_loss

        fce_loss = total_loss / batch_size

        # Get FCE weight from configuration
        fce_weight = pn_loss.get('fce_weight', 0.5)  # Default weight for FCE loss
        return fce_weight * fce_loss

    def fce_loss_simple(self, z_rm, z_target, F_reference, text_tokens, pn_loss):
        """
        Simplified FCE loss implementation following the paper exactly.

        For each target t_k, we have:
        - Positive query: q_k = r_k + s_k
        - Synthetic negative examples: f'_i = r_k + s_i (i ≠ k)

        Loss: L2 = -log(exp(κ(t_k,q_k)) / (exp(κ(t_k,q_k)) + Σ exp(κ(t_k,f'_i))))
        """
        batch_size = z_rm.shape[0]
        if batch_size < 2:
            return torch.tensor(0.0, device=z_rm.device, requires_grad=True)

        total_loss = 0.0
        eps = 1e-8

        # 确保温度参数在合理范围内
        temp = torch.clamp(self.temp, min=1e-2, max=1.0)

        for k in range(batch_size):
            # Current target and positive query
            target_k = z_target[k:k+1]  # [1, 32, embed_dim]
            query_k = z_rm[k:k+1]  # [1, embed_dim]
            ref_k = F_reference[k:k+1]  # [1, 32, hidden_size]

            # Calculate positive similarity - 使用平均池化而不是最大池化来避免数值不稳定
            pos_sim_matrix = torch.matmul(
                query_k.unsqueeze(1),  # [1, 1, embed_dim]
                target_k.permute(0, 2, 1)  # [1, embed_dim, 32]
            )  # [1, 1, 32]
            pos_sim = pos_sim_matrix.mean(dim=-1).squeeze()  # 使用平均值而不是最大值

            # Create synthetic negative examples f'_i = r_k + s_i (i ≠ k)
            neg_sims = []
            for i in range(batch_size):
                if i != k:
                    # Create text tokens for sample i
                    text_i = type(text_tokens)(
                        input_ids=text_tokens.input_ids[i:i+1],
                        attention_mask=text_tokens.attention_mask[i:i+1]
                    )
                    # Synthetic composition: ref_k + text_i
                    synthetic_comp = self.encode_fusion(ref_k, text_i)

                    # Calculate similarity with target_k - 同样使用平均池化
                    neg_sim_matrix = torch.matmul(
                        synthetic_comp.unsqueeze(1),  # [1, 1, embed_dim]
                        target_k.permute(0, 2, 1)  # [1, embed_dim, 32]
                    )  # [1, 1, 32]
                    neg_sim = neg_sim_matrix.mean(dim=-1).squeeze()  # 使用平均值
                    neg_sims.append(neg_sim)

            if neg_sims:
                neg_sims = torch.stack(neg_sims)

                # 数值稳定的FCE InfoNCE loss计算
                # 使用log-sum-exp技巧避免数值溢出
                pos_logit = pos_sim / temp
                neg_logits = neg_sims / temp

                # 找到最大值用于数值稳定
                max_logit = torch.max(torch.cat([pos_logit.unsqueeze(0), neg_logits]))

                # 数值稳定的softmax计算
                pos_exp = torch.exp(pos_logit - max_logit)
                neg_exp = torch.exp(neg_logits - max_logit).sum()

                # 避免除零和log(0)
                denominator = pos_exp + neg_exp + eps
                sample_loss = -torch.log(pos_exp / denominator + eps)

                # 检查并处理NaN/Inf
                if torch.isnan(sample_loss) or torch.isinf(sample_loss):
                    sample_loss = torch.tensor(0.0, device=z_rm.device, requires_grad=True)

                total_loss += sample_loss

        fce_loss = total_loss / batch_size
        fce_weight = pn_loss.get('fce_weight', 0.5)

        # 最终检查确保返回值是有效的
        final_loss = fce_weight * fce_loss
        if torch.isnan(final_loss) or torch.isinf(final_loss):
            final_loss = torch.tensor(0.0, device=z_rm.device, requires_grad=True)

        return final_loss

    def forward(self, samples, labels, pn_loss, warmup):
        image_embeds = samples["image"]
        target_embeds = samples["target"]
        text = samples["text_input"]
        image_embeds = self.ln_vision(image_embeds) # avoid repeated computation of laynorm in image encode
        text_tokens = self.tokenizer(
            text,
            padding="max_length",
            truncation=True,
            max_length=self.max_txt_len,
            return_tensors="pt",
        ).to(image_embeds.device)
        text_tokens = text_tokens.to(image_embeds.device)
        query_tokens = self.query_tokens.expand(image_embeds.shape[0], -1, -1)
        F_reference = self.encode_image(image_embeds, query_tokens, ln=False)
        target_embeds = self.ln_vision(target_embeds)
        F_target = self.encode_image(target_embeds, query_tokens, ln=False)
        z_target = F.normalize(self.vision_proj(F_target), dim=-1)
        loss_dict = {}

        # fusion encode
        z_rm = self.encode_fusion(F_reference, text_tokens)
        sim_r2t = torch.matmul(
        z_rm.unsqueeze(1).unsqueeze(1), z_target.permute(0, 2, 1)
        ).squeeze()
        sim_r2t, _ = sim_r2t.max(-1)
        lrm = self.robust_infoNCE(sim_r2t, labels, pn_loss)
        loss_dict['lrm'] = lrm

        # Add FCE (Fusion with Component-level Examples) loss
        # Check if FCE loss is enabled in pn_loss configuration
        if pn_loss.get('enable_fce', False) and z_rm.shape[0] > 1:  # Need at least 2 samples for FCE
            # Use the simpler, more efficient FCE implementation
            fce_loss = self.fce_loss_simple(
                z_rm, z_target, F_reference, text_tokens, pn_loss
            )
            loss_dict['fce_loss'] = fce_loss

            # Optionally combine with original loss (controlled by configuration)
            if pn_loss.get('combine_fce_with_original', True):
                total_loss = lrm + fce_loss
                loss_dict['total_loss'] = total_loss

        return loss_dict

    @torch.no_grad()
    def inference(self, F_reference, F_target, text):
        text_tokens = self.tokenizer(
                text,
                padding="max_length",
                truncation=True,
                max_length=self.max_txt_len,
                return_tensors="pt",
            ).to(F_reference.device)
        z_rm = self.encode_fusion(F_reference, text_tokens)
        z_target = F.normalize(self.vision_proj(F_target), dim=-1)
        sim_t2q = torch.matmul(
            z_rm.unsqueeze(1).unsqueeze(1), z_target.permute(0, 2, 1)
        ).squeeze()
        sim_i2t, _ = sim_t2q.max(-1)
        return sim_i2t

    @classmethod
    def from_config(cls, cfg):
        vit_model = cfg.get("vit_model", "eva_clip_g")
        img_size = cfg.get("image_size")
        num_query_token = cfg.get("num_query_token")
        cross_attention_freq = cfg.get("cross_attention_freq", 2)

        drop_path_rate = cfg.get("drop_path_rate", 0)
        use_grad_checkpoint = cfg.get("use_grad_checkpoint", False)
        vit_precision = cfg.get("vit_precision", "fp16")
        freeze_vit = cfg.get("freeze_vit", True)

        max_txt_len = cfg.get("max_txt_len", 32)

        model = cls(
            vit_model=vit_model,
            img_size=img_size,
            drop_path_rate=drop_path_rate,
            use_grad_checkpoint=use_grad_checkpoint,
            vit_precision=vit_precision,
            freeze_vit=freeze_vit,
            num_query_token=num_query_token,
            cross_attention_freq=cross_attention_freq,
            max_txt_len=max_txt_len,
        )
        model.load_checkpoint_from_config(cfg)

        return model
