#!/bin/bash
# FCE硬负样本训练脚本 - 同时使用lrm和fce损失函数
# dataset: CIRR或FashionIQ
# noise_ratio: 噪声比例（简化版仍可设置，但对训练无实际影响）
# gpu: 指定要使用的GPU设备编号（0, 1, 2...）
# FCE: Fusion with Component-level Examples 硬负样本挖掘

your_exp_name=8-18-fce-combined
shuffle_seed=42
seed=42
dataset=CIRR
# 指定使用的GPU编号，注意这里直接使用数字，不要加引号
gpu=1

noise_ratio=0.0

# FCE相关配置
enable_fce=true  # 启用FCE硬负样本
fce_weight=0.5   # FCE损失权重，推荐0.3-0.7
combine_fce_with_original=true  # 与原始损失结合

echo "指定使用GPU: ${gpu}"
echo "FCE配置: enable_fce=${enable_fce}, fce_weight=${fce_weight}"

python src/precompute_train.py \
    --exp_name "${your_exp_name}" \
    --shuffle_seed ${shuffle_seed} \
    --seed ${seed} \
    --dataset ${dataset} \
    --noise_ratio ${noise_ratio} \
    --batch_size 64 \
    --num_epochs 30 \
    --lr "1e-5" \
    --positive_loss "RCL" \
    --trade_off 1.0 \
    --enable_fce \
    --fce_weight ${fce_weight} \
    --combine_fce_with_original \
    --save_training \
    --gpu ${gpu}