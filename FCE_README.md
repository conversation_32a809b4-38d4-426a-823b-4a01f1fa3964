# FCE (Fusion with Component-level Examples) 硬负样本实现

本项目实现了基于论文 "Learning with Synthetic Composition Examples" 的FCE硬负样本挖掘方法，用于改进组合图像检索任务的对比学习。

## 📖 FCE原理

### 传统方法的问题
- 使用查询级别的负样本：`q'_i`（完全不同的查询）
- 这些负样本通常很容易区分，提供的学习信号有限

### FCE方法的改进
- 使用组件级别的负样本：`f'_i = r + s'_i`
- 将当前参考图像 `r` 与其他样本的文本特征 `s'_i` 组合
- 创建更难的负样本，更接近目标图像
- 提供更有效的学习信号

### 损失函数
```
L2 = -log(exp(κ(t,q)) / (exp(κ(t,q)) + Σ exp(κ(t,f'_i))))
```
其中：
- `t`: 目标图像嵌入
- `q`: 正样本组合查询 (r + s)
- `f'_i`: 合成组合样本 (r + s'_i)
- `κ`: 余弦相似度函数

## 🚀 快速开始

### 1. 基本使用

```python
# 在训练脚本中启用FCE
pn_loss = {
    'positive_loss': 'RCL',
    'negative_loss': 'None',
    'trade_off': 1.0,
    
    # FCE配置
    'enable_fce': True,
    'fce_weight': 0.5,
    'combine_fce_with_original': True
}
```

### 2. 使用训练脚本

```bash
# 启用FCE训练
python train_with_fce.py --enable_fce --fce_weight 0.5 --batch_size 64

# 查看使用示例
python train_with_fce.py --examples
```

### 3. 使用配置文件

```python
from fce_configs import get_config

# 获取预定义配置
config = get_config('basic_fce')

# 查看所有配置
python fce_configs.py --all
```

## ⚙️ 配置参数

### FCE相关参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `enable_fce` | bool | False | 是否启用FCE损失 |
| `fce_weight` | float | 0.5 | FCE损失权重 (0.0-1.0) |
| `combine_fce_with_original` | bool | True | 是否与原始损失结合 |

### 推荐配置

| 场景 | 配置名称 | FCE权重 | 适用情况 |
|------|----------|---------|----------|
| 首次使用 | `basic_fce` | 0.5 | 大多数情况 |
| 大批次训练 | `strong_fce` | 0.7 | batch_size >= 32 |
| 小批次训练 | `weak_fce` | 0.3 | batch_size < 8 |
| 研究FCE效果 | `pure_fce` | 1.0 | 只使用FCE损失 |
| 基线对比 | `traditional` | 0.0 | 不使用FCE |

## 📊 实验结果

### 预期改进
- 更好的图像-文本融合
- 提高检索准确率
- 更稳定的训练过程
- 更好的泛化能力

### 训练监控
训练过程中会显示以下损失信息：
```
[1/5] lrm: 0.234, fce: 0.156, total: 0.390
```
- `lrm`: 原始robust InfoNCE损失
- `fce`: FCE硬负样本损失
- `total`: 组合总损失

## 🔧 实现细节

### 核心实现
- 位置：`src/lavis/models/blip2_models/blip2_qformer_cir_image_diff_features.py`
- 方法：`fce_loss_simple()` 和 `synthetic_composition_loss()`

### 数值稳定性
- 使用log-sum-exp技巧避免数值溢出
- 温度参数限制在合理范围内
- NaN/Inf检测和处理
- 使用平均池化而不是最大池化

### 内存优化
- 批量处理合成样本
- 避免重复计算
- 适当的梯度累积

## 📝 使用示例

### 示例1：基础FCE训练
```bash
python train_with_fce.py \
    --enable_fce \
    --fce_weight 0.5 \
    --batch_size 64 \
    --num_epochs 10 \
    --dataset CIRR
```

### 示例2：不同FCE权重实验
```bash
# 弱FCE
python train_with_fce.py --enable_fce --fce_weight 0.3 --exp_name weak_fce

# 强FCE  
python train_with_fce.py --enable_fce --fce_weight 0.7 --exp_name strong_fce
```

### 示例3：配置文件使用
```python
from fce_configs import get_config, get_recommended_config

# 使用预定义配置
config = get_config('basic_fce')

# 根据批次大小自动推荐
config = get_recommended_config(dataset='CIRR', batch_size=32)
```

## ⚠️ 注意事项

### 批次大小要求
- FCE需要批次大小至少为2
- 推荐批次大小 >= 4 以获得更好效果
- 大批次大小通常能获得更好的FCE效果

### 权重调优
- 从0.5开始实验
- 根据验证结果调整
- 通常在0.3-0.7之间效果最好

### 计算开销
- FCE会增加一些计算开销
- 开销与批次大小成正比
- 通常增加10-20%的训练时间

## 🐛 故障排除

### 常见问题

1. **NaN损失**
   - 检查批次大小是否 >= 2
   - 降低学习率
   - 减小FCE权重

2. **内存不足**
   - 减小批次大小
   - 使用梯度累积

3. **训练不稳定**
   - 降低FCE权重
   - 检查数据质量

### 调试工具
```python
# 测试FCE实现
python test_fce_implementation.py

# 查看配置指南
python fce_configs.py --guide
```

## 📚 参考文献

- 原始论文：Learning with Synthetic Composition Examples
- InfoNCE损失：Representation Learning with Contrastive Predictive Coding
- BLIP2模型：Bootstrapping Language-Image Pre-training

## 🤝 贡献

欢迎提交问题和改进建议！

## 📄 许可证

本实现遵循原项目的许可证。
