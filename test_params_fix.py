#!/usr/bin/env python3
"""
测试参数修复的脚本
验证Params对象是否正确包含所有FCE相关属性
"""

import sys
import os

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_params_attributes():
    """测试Params对象的属性"""
    print("测试Params对象属性...")
    
    # 模拟命令行参数
    class MockArgs:
        def __init__(self):
            # 基本参数
            self.dataset = 'CIRR'
            self.noise_ratio = 0.0
            self.nc_type = 'mix'
            self.method = 'image_diff'
            self.save_training = True
            self.backbone = 'pretrain'
            self.num_workers = 9
            self.weight_decay = 0.05
            self.lr = 1e-5
            self.batch_size = 64
            self.num_epochs = 30
            self.seed = 42
            self.shuffle_seed = 42
            self.timestamp = '2024-08-18-test'
            self.exp_name = 'test_fce'
            
            # 损失函数参数
            self.positive_loss = 'RCL'
            self.negative_loss = 'None'
            self.trade_off = 1.0
            
            # FCE参数
            self.enable_fce = True
            self.fce_weight = 0.5
            self.combine_fce_with_original = True
    
    try:
        # 导入utility模块
        import utility
        
        # 创建mock args
        args = MockArgs()
        
        # 初始化Params
        utility.Params.initialize(args)
        params = utility.Params()
        
        print("Params对象创建成功！")
        
        # 检查基本属性
        print(f"  dataset: {params.dataset}")
        print(f"  batch_size: {params.batch_size}")
        print(f"  num_epochs: {params.num_epochs}")
        print(f"  save_training: {params.save_training}")
        print(f"  exp_name: {params.exp_name}")
        print(f"  timestamp: {params.timestamp}")
        
        # 检查pn_loss配置
        print("\npn_loss配置:")
        for key, value in params.pn_loss.items():
            print(f"  {key}: {value}")
        
        # 验证FCE相关属性
        assert 'enable_fce' in params.pn_loss, "缺少enable_fce配置"
        assert 'fce_weight' in params.pn_loss, "缺少fce_weight配置"
        assert 'combine_fce_with_original' in params.pn_loss, "缺少combine_fce_with_original配置"
        
        assert params.pn_loss['enable_fce'] == True, "enable_fce值不正确"
        assert params.pn_loss['fce_weight'] == 0.5, "fce_weight值不正确"
        assert params.pn_loss['combine_fce_with_original'] == True, "combine_fce_with_original值不正确"
        
        print("\n✓ 所有Params属性验证通过！")
        return True
        
    except Exception as e:
        print(f"✗ Params测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_function_signature():
    """测试函数签名是否正确"""
    print("\n测试函数签名...")
    
    try:
        # 导入训练模块
        from precompute_train import blip_finetune
        
        # 检查函数签名
        import inspect
        sig = inspect.signature(blip_finetune)
        params = list(sig.parameters.keys())
        
        print(f"blip_finetune函数参数: {params}")
        
        # 应该只有一个参数，且名为params或类似
        assert len(params) == 1, f"函数应该只有一个参数，实际有{len(params)}个"
        
        param_name = params[0]
        print(f"参数名称: {param_name}")
        
        # 参数名应该是params而不是args
        if param_name == 'params':
            print("✓ 函数签名正确！")
            return True
        else:
            print(f"⚠ 参数名为'{param_name}'，建议改为'params'")
            return True  # 仍然可以工作，只是命名不一致
        
    except Exception as e:
        print(f"✗ 函数签名测试失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("FCE参数修复验证")
    print("=" * 50)
    
    tests = [
        test_params_attributes,
        test_function_signature
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试 {test.__name__} 失败: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有参数修复验证通过！")
        print("\n现在可以正常运行FCE训练:")
        print("  bash train.sh")
        print("\n或者运行基线对比:")
        print("  bash train_baseline.sh")
    else:
        print("✗ 部分测试失败，请检查修复")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
