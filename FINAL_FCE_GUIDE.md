# FCE硬负样本训练 - 最终使用指南

## ✅ 修复完成

已成功修复`AttributeError: 'Params' object has no attribute 'positive_loss'`错误。

### 主要修复内容：
1. **函数参数统一**：`blip_finetune(params)`函数现在正确使用`params`对象
2. **属性访问修正**：所有`args.xxx`改为`params.xxx`
3. **FCE配置集成**：FCE参数已正确集成到`Params`类的`pn_loss`字典中

## 🚀 立即开始使用

### 1. 启用FCE训练（推荐）
```bash
bash train.sh
```

### 2. 基线对比训练
```bash
bash train_baseline.sh
```

### 3. FCE权重实验
```bash
bash train_fce_experiments.sh
```

## 📊 FCE损失组合方式

### 当前实现（同时使用lrm和fce）：
```python
# 在训练循环中
lrm_loss = loss_dict['lrm']  # 原始robust InfoNCE损失

if enable_fce and 'fce_loss' in loss_dict:
    fce_loss = loss_dict['fce_loss']  # FCE硬负样本损失
    total_loss = lrm_loss + fce_loss  # 直接相加
else:
    total_loss = lrm_loss  # 仅使用原始损失
```

### FCE权重控制：
```python
# 在模型中
fce_loss = fce_weight * raw_fce_loss  # 0.5 * raw_fce_loss
```

### 最终损失：
```
total_loss = lrm_loss + (fce_weight * raw_fce_loss)
```

## ⚙️ 配置参数

### train.sh中的FCE配置：
```bash
# FCE相关配置
enable_fce=true      # 启用FCE硬负样本
fce_weight=0.5       # FCE损失权重，推荐0.3-0.7
combine_fce_with_original=true  # 与原始损失结合

# 训练命令
python src/precompute_train.py \
    --enable_fce \
    --fce_weight ${fce_weight} \
    --combine_fce_with_original \
    # ... 其他参数
```

## 📈 训练监控

### 控制台输出：
```
FCE (Fusion with Component-level Examples) 配置:
============================================================
启用FCE: True
FCE权重: 0.5
与原始损失结合: True
批次大小: 64 (FCE需要 >= 2)
FCE工作原理: 使用合成组合样本 f'_i = r + s'_i 作为硬负样本
预期效果: 更好的图像-文本融合，提高检索准确率
============================================================

[1/30] lrm: 0.234, fce: 0.156, total: 0.390
```

### 日志记录：
```
使用组合损失: lrm=0.2340, fce=0.1560, total=0.3900
```

## 🔧 参数调优建议

### 1. FCE权重选择：
- **0.3**：保守设置，适合初次尝试
- **0.5**：平衡设置，推荐默认值
- **0.7**：激进设置，适合大批次训练

### 2. 批次大小：
- **最小值**：2（FCE工作的最低要求）
- **推荐值**：64或更大
- **注意**：批次越大，FCE效果通常越好

### 3. 学习率：
- 可能需要略微降低，因为总损失增大了
- 建议从原来的学习率开始，根据训练稳定性调整

## 🧪 实验流程建议

### 第一步：基线对比
```bash
# 运行不使用FCE的基线
bash train_baseline.sh
```

### 第二步：FCE训练
```bash
# 运行使用FCE的训练
bash train.sh
```

### 第三步：权重调优
```bash
# 测试不同FCE权重
bash train_fce_experiments.sh
```

### 第四步：结果对比
比较各实验的验证准确率，选择最佳配置。

## 📁 重要文件

### 核心实现：
- `src/lavis/models/blip2_models/blip2_qformer_cir_image_diff_features.py` - FCE损失实现
- `src/precompute_train.py` - 训练脚本（已修复）
- `src/utility.py` - 参数管理（包含FCE配置）

### 训练脚本：
- `train.sh` - FCE训练脚本
- `train_baseline.sh` - 基线对比脚本
- `train_fce_experiments.sh` - 权重实验脚本

### 测试和文档：
- `test_params_fix.py` - 参数修复验证
- `FCE_README.md` - 详细技术文档
- `FCE_USAGE_SUMMARY.md` - 使用总结

## ⚠️ 注意事项

### 1. 批次大小要求：
- FCE需要批次大小至少为2
- 如果批次大小为1，会自动跳过FCE计算

### 2. 内存使用：
- FCE会增加约10-20%的计算开销
- 如果内存不足，可以减小批次大小

### 3. 数值稳定性：
- 已实现数值稳定的FCE计算
- 包含NaN/Inf检测和处理

## 🎯 预期效果

### 1. 训练改进：
- 更难的负样本提供更好的学习信号
- 促进图像和文本特征的更好融合
- 更稳定的训练过程

### 2. 性能提升：
- 预期在检索任务上获得更好的准确率
- 更好的泛化能力
- 改进的组合查询理解

## 🚀 开始训练

现在一切就绪，可以开始FCE训练：

```bash
# 确保在项目根目录
cd /path/to/TME-master

# 启动FCE训练
bash train.sh

# 或者运行基线对比
bash train_baseline.sh
```

训练将自动：
1. 显示FCE配置信息
2. 同时使用lrm和fce损失
3. 实时监控训练进度
4. 保存最佳模型
5. 记录详细日志

## 📞 故障排除

如果遇到问题，请检查：
1. 批次大小是否 >= 2
2. GPU内存是否充足
3. 参数配置是否正确
4. 日志文件中的错误信息

祝训练顺利！🎉
