# FCE硬负样本实现使用总结

## 🎯 实现概述

已成功在TME模型中实现了FCE (Fusion with Component-level Examples) 硬负样本挖掘方法，现在**同时使用lrm和fce两个损失函数**进行训练。

## 🔧 核心修改

### 1. 模型层面 (`src/lavis/models/blip2_models/blip2_qformer_cir_image_diff_features.py`)
- 添加了 `fce_loss_simple()` 方法实现FCE损失计算
- 添加了数值稳定性处理，避免NaN/Inf问题
- 返回独立的 `lrm` 和 `fce_loss`，由训练脚本决定如何组合

### 2. 训练脚本 (`src/precompute_train.py`)
- 添加FCE相关命令行参数解析
- **修改损失计算逻辑：`loss = lrm_loss + fce_loss`**
- 添加FCE训练状态监控和日志记录
- 添加FCE配置信息打印

### 3. 配置文件 (`src/utility.py`)
- 添加FCE参数的默认值处理

## 🚀 使用方法

### 方法1：使用修改后的train.sh
```bash
# 启用FCE训练（推荐）
bash train.sh
```

### 方法2：直接命令行
```bash
python src/precompute_train.py \
    --exp_name "fce-experiment" \
    --dataset CIRR \
    --enable_fce \
    --fce_weight 0.5 \
    --combine_fce_with_original \
    --batch_size 64 \
    --num_epochs 30 \
    --positive_loss "RCL" \
    --trade_off 1.0 \
    --save_training \
    --gpu 1
```

### 方法3：基线对比
```bash
# 不使用FCE的基线训练
bash train_baseline.sh
```

### 方法4：FCE权重实验
```bash
# 测试不同FCE权重
bash train_fce_experiments.sh
```

## ⚙️ FCE参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--enable_fce` | flag | False | 启用FCE硬负样本损失 |
| `--fce_weight` | float | 0.5 | FCE损失权重 (0.0-1.0) |
| `--combine_fce_with_original` | flag | True | 与原始损失结合 |

## 📊 损失函数组合

### 当前实现（同时使用两个损失）：
```python
if enable_fce and fce_loss exists:
    total_loss = lrm_loss + fce_loss  # 直接相加
else:
    total_loss = lrm_loss  # 仅使用原始损失
```

### FCE损失计算：
```python
fce_loss = fce_weight * raw_fce_loss
```

### 最终损失：
```
total_loss = lrm_loss + (fce_weight * raw_fce_loss)
```

## 🔍 训练监控

训练过程中会显示：
```
[1/30] lrm: 0.234, fce: 0.156, total: 0.390
```

日志中会记录：
```
使用组合损失: lrm=0.2340, fce=0.1560, total=0.3900
```

## 📈 预期效果

1. **更好的硬负样本**：FCE创建的合成组合样本比传统负样本更难区分
2. **改进的融合**：促进图像和文本特征的更好融合
3. **提高准确率**：预期在检索任务上获得更好的性能
4. **稳定训练**：数值稳定的实现确保训练过程稳定

## 🧪 实验建议

### 1. 基础实验
```bash
# 基线（无FCE）
bash train_baseline.sh

# FCE训练
bash train.sh
```

### 2. 权重调优
```bash
# 测试不同FCE权重
bash train_fce_experiments.sh
```

### 3. 参数建议
- **批次大小**：建议 >= 4，FCE需要多个样本才能工作
- **FCE权重**：推荐从0.5开始，根据结果调整到0.3-0.7
- **学习率**：可能需要略微降低，因为总损失增大了

## 🔧 故障排除

### 1. NaN损失
- 检查批次大小是否 >= 2
- 降低学习率
- 减小FCE权重

### 2. 内存不足
- 减小批次大小
- FCE会增加约10-20%的计算开销

### 3. 训练不稳定
- 降低FCE权重到0.3
- 检查数据质量

## 📁 相关文件

### 核心实现
- `src/lavis/models/blip2_models/blip2_qformer_cir_image_diff_features.py` - FCE损失实现
- `src/precompute_train.py` - 训练脚本

### 配置和脚本
- `train.sh` - FCE训练脚本
- `train_baseline.sh` - 基线对比脚本
- `train_fce_experiments.sh` - FCE权重实验脚本

### 文档和示例
- `FCE_README.md` - 详细文档
- `example_fce_usage.py` - 使用示例
- `fce_configs.py` - 配置示例
- `test_fce_args.py` - 参数测试

## ✅ 验证清单

- [x] FCE损失函数实现
- [x] 数值稳定性处理
- [x] 命令行参数解析
- [x] 训练脚本集成
- [x] 同时使用lrm+fce损失
- [x] 训练监控和日志
- [x] 配置文件更新
- [x] 测试脚本
- [x] 文档和示例

## 🎉 总结

FCE硬负样本功能已完全集成到TME训练流程中，现在可以：

1. **启用FCE**：使用 `--enable_fce` 参数
2. **调整权重**：使用 `--fce_weight` 控制FCE贡献
3. **监控训练**：实时查看lrm、fce和总损失
4. **对比实验**：轻松切换FCE开关进行对比

开始训练：`bash train.sh` 🚀
